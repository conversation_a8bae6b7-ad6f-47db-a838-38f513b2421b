import React, { useEffect, useState } from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { useAuthStore } from "../stores/authStore";
import OnboardingNavigator from "./OnboardingNavigator";
import MainNavigator from "./MainNavigator";
import { View, ActivityIndicator } from "react-native";
import { colors } from "../utils/styles";

export type RootStackParamList = {
  Onboarding: undefined;
  Main: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export default function RootNavigator() {
  const { isAuthenticated, user, checkFarmerProfileCompletion } =
    useAuthStore();
  const [isCheckingProfile, setIsCheckingProfile] = useState(false);
  const [shouldShowOnboarding, setShouldShowOnboarding] = useState(false);

  useEffect(() => {
    const checkUserProfile = async () => {
      if (isAuthenticated && user?.role === "farmer") {
        setIsCheckingProfile(true);
        try {
          const isProfileComplete = await checkFarmerProfileCompletion();
          setShouldShowOnboarding(!isProfileComplete);
        } catch (error) {
          console.log("Error checking farmer profile in RootNavigator:", error);
          // If check fails, assume profile is incomplete
          setShouldShowOnboarding(true);
        } finally {
          setIsCheckingProfile(false);
        }
      } else {
        setShouldShowOnboarding(false);
        setIsCheckingProfile(false);
      }
    };

    checkUserProfile();
  }, [isAuthenticated, user?.role, checkFarmerProfileCompletion]);

  // Show loading spinner while checking farmer profile
  if (isCheckingProfile) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: colors.green[50],
        }}
      >
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {isAuthenticated && !shouldShowOnboarding ? (
        <Stack.Screen name="Main" component={MainNavigator} />
      ) : (
        <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
      )}
    </Stack.Navigator>
  );
}
