import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { useAuthStore } from "../stores/authStore";
import WelcomeScreen from "../screens/onboarding/WelcomeScreen";
import LoginScreen from "../screens/onboarding/LoginScreen";
import SignupScreen from "../screens/onboarding/SignupScreen";
import ForgotPasswordScreen from "../screens/onboarding/ForgotPasswordScreen";
import FarmerDetailsScreen from "../screens/onboarding/FarmerDetailsScreen";
import VerificationScreen from "../screens/onboarding/VerificationScreen";
export type OnboardingStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Signup: undefined;
  ForgotPassword: undefined;
  FarmerDetails: undefined;
  Verification: { email?: string };
};

const Stack = createStackNavigator<OnboardingStackParamList>();

export default function OnboardingNavigator() {
  const { isAuthenticated, user } = useAuthStore();

  // Determine the initial screen based on authentication status
  const getInitialRouteName = (): keyof OnboardingStackParamList => {
    if (isAuthenticated && user) {
      // If authenticated but onboarding not complete, go to appropriate screen
      if (!user.onboardingComplete) {
        if (user.role === "farmer") {
          return "FarmerDetails";
        }
        // For buyers, this shouldn't happen as they complete onboarding after email verification
        // But if it does, send them to verification
        return "Verification";
      }
    }
    // Default to welcome screen for non-authenticated users
    return "Welcome";
  };

  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false }}
      initialRouteName={getInitialRouteName()}
    >
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Signup" component={SignupScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="FarmerDetails" component={FarmerDetailsScreen} />
      <Stack.Screen name="Verification" component={VerificationScreen} />
    </Stack.Navigator>
  );
}
