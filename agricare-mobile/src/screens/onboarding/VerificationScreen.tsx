import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { StackNavigationProp } from "@react-navigation/stack";
import { RouteProp } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import { OnboardingStackParamList } from "../../navigation/OnboardingNavigator";
import { colors, commonStyles } from "../../utils/styles";
import { useAuthStore } from "../../stores/authStore";
import { useAlert } from "../../hooks/useAlert";
import Alert from "../../components/Alert";

type VerificationScreenNavigationProp = StackNavigationProp<
  OnboardingStackParamList,
  "Verification"
>;

type VerificationScreenRouteProp = RouteProp<
  OnboardingStackParamList,
  "Verification"
>;

interface Props {
  navigation: VerificationScreenNavigationProp;
  route: VerificationScreenRouteProp;
}

export default function VerificationScreen({ navigation, route }: Props) {
  const { email } = route.params || {};
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [timer, setTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const {
    verifyEmail,
    resendVerification,
    checkFarmerProfileCompletion,
    user,
  } = useAuthStore();
  const { alert, showError, showSuccess, hideAlert } = useAlert();

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer(timer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      // Auto-focus next input
      if (value && index < 5) {
        // In React Native, we would need refs to focus next input
        // For now, we'll keep it simple
      }
    }
  };

  const handleKeyDown = (index: number, key: string) => {
    if (key === "Backspace" && !otp[index] && index > 0) {
      // Focus previous input on backspace
      // In React Native, we would need refs to focus previous input
    }
  };

  const handleResend = async () => {
    if (!email) {
      showError("Email address not found. Please go back and try again.");
      return;
    }

    setIsResending(true);

    try {
      const result = await resendVerification(email);

      if (result.success) {
        setTimer(30);
        setCanResend(false);
        setOtp(["", "", "", "", "", ""]);
        showSuccess("Verification code has been resent to your email");
      } else {
        showError(result.message || "Failed to resend verification code");
      }
    } catch (error) {
      showError("An unexpected error occurred. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const handleSubmit = async () => {
    const otpValue = otp.join("");
    if (otpValue.length !== 6) {
      showError("Please enter the complete verification code");
      return;
    }

    if (!email) {
      showError("Email address not found. Please go back and try again.");
      return;
    }

    setIsLoading(true);

    try {
      const result = await verifyEmail({
        email,
        code: otpValue,
      });

      if (result.success) {
        showSuccess("Email verified successfully! Welcome to AgriCare!");

        // Check if user is a farmer and needs to complete profile
        if (user?.role === "farmer") {
          try {
            const isProfileComplete = await checkFarmerProfileCompletion();

            if (!isProfileComplete) {
              // Navigate to farmer details screen
              setTimeout(() => {
                navigation.navigate("FarmerDetails");
              }, 1500);
              return;
            }
          } catch (error) {
            console.log("Error checking farmer profile:", error);
            // If check fails, assume profile is incomplete and navigate to farmer details
            setTimeout(() => {
              navigation.navigate("FarmerDetails");
            }, 1500);
            return;
          }
        }

        // For buyers or farmers with complete profiles,
        // the auth state will automatically handle navigation to the main app
        // No need to navigate manually
      } else {
        showError(result.message || "Invalid verification code");
      }
    } catch (error) {
      showError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={colors.gray[600]} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Verification</Text>
          <View style={styles.headerSpacer} />
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Title */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Enter Verification Code</Text>
          <Text style={styles.subtitle}>
            We've sent a 6-digit code to{"\n"}
            <Text style={styles.emailAddress}>{email || "your email"}</Text>
          </Text>
        </View>

        {/* OTP Input */}
        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              value={digit}
              onChangeText={(value) => handleOtpChange(index, value)}
              maxLength={1}
              keyboardType="numeric"
              style={styles.otpInput}
            />
          ))}
        </View>

        {/* Timer and Resend */}
        <View style={styles.timerContainer}>
          {!canResend ? (
            <Text style={styles.timerText}>
              Resend code in <Text style={styles.timerHighlight}>{timer}s</Text>
            </Text>
          ) : (
            <TouchableOpacity onPress={handleResend} disabled={isResending}>
              {isResending ? (
                <ActivityIndicator color={colors.primary} size="small" />
              ) : (
                <Text style={styles.resendText}>Resend Code</Text>
              )}
            </TouchableOpacity>
          )}
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          onPress={handleSubmit}
          disabled={otp.some((digit) => !digit) || isLoading}
          style={[
            commonStyles.button,
            (otp.some((digit) => !digit) || isLoading) && styles.buttonDisabled,
          ]}
        >
          {isLoading ? (
            <ActivityIndicator color={colors.white} size="small" />
          ) : (
            <Text style={commonStyles.buttonText}>Verify & Continue</Text>
          )}
        </TouchableOpacity>

        {/* Help Text */}
        <Text style={styles.helpText}>
          Didn't receive the code?{" "}
          <Text style={styles.helpLink}>Contact Support</Text>
        </Text>
      </View>

      {/* Alert Component */}
      <Alert
        visible={alert.visible}
        type={alert.type}
        title={alert.title}
        message={alert.message}
        onClose={hideAlert}
        duration={alert.duration}
        position="top"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.green[50],
  },
  header: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    paddingHorizontal: 24,
    paddingVertical: 16,
    paddingTop: 50, // Account for status bar
    zIndex: 10,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerTitle: {
    flex: 1,
    textAlign: "center",
    fontSize: 18,
    fontWeight: "600",
    color: colors.gray[800],
  },
  headerSpacer: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingTop: 100, // Account for fixed header
    paddingHorizontal: 24,
    paddingBottom: 32,
    justifyContent: "center",
  },
  titleContainer: {
    alignItems: "center",
    marginBottom: 40,
    marginTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: colors.gray[800],
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: "center",
    lineHeight: 24,
  },
  emailAddress: {
    fontWeight: "500",
    color: colors.gray[800],
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 12,
    marginBottom: 32,
  },
  otpInput: {
    width: 48,
    height: 48,
    borderWidth: 2,
    borderColor: colors.gray[200],
    borderRadius: 16,
    textAlign: "center",
    fontSize: 18,
    fontWeight: "600",
    backgroundColor: colors.white,
  },
  timerContainer: {
    alignItems: "center",
    marginBottom: 24,
  },
  timerText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  timerHighlight: {
    fontWeight: "500",
    color: colors.primary,
  },
  resendText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  buttonDisabled: {
    backgroundColor: colors.gray[300],
  },
  helpText: {
    fontSize: 12,
    color: colors.gray[500],
    textAlign: "center",
    marginTop: 24,
  },
  helpLink: {
    color: colors.primary,
  },
});
