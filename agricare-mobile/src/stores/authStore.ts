import { create } from "zustand";
import {
  User,
  AuthState,
  SignupRequest,
  LoginRequest,
  VerifyEmailRequest,
} from "../types";
import { authService, AuthUser } from "../services/authService";

interface AuthStore extends AuthState {
  // Actions
  signup: (data: SignupRequest) => Promise<{
    success: boolean;
    message: string;
    errors?: Record<string, string>;
  }>;
  login: (data: LoginRequest) => Promise<{
    success: boolean;
    message: string;
    errors?: Record<string, string>;
  }>;
  logout: () => Promise<void>;
  verifyEmail: (
    data: VerifyEmailRequest
  ) => Promise<{ success: boolean; message: string }>;
  resendVerification: (
    email: string
  ) => Promise<{ success: boolean; message: string }>;
  checkAuthStatus: () => Promise<void>;
  initializeApp: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  markOnboardingComplete: () => void;
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: undefined,

  signup: async (data: SignupRequest) => {
    set({ isLoading: true, error: undefined });

    try {
      const response = await authService.signup(data);

      if (response.success && response.data) {
        const user = response.data.user as User;
        set({
          user,
          isAuthenticated: false, // Don't authenticate immediately - wait for verification
          isLoading: false,
          error: undefined,
        });

        return {
          success: true,
          message: response.message || "Account created successfully",
        };
      } else {
        set({ isLoading: false, error: response.message });
        return {
          success: false,
          message: response.message || "Signup failed",
          errors: response.errors,
        };
      }
    } catch (error: any) {
      const errorMessage = error.message || "Network error occurred";
      set({ isLoading: false, error: errorMessage });
      return {
        success: false,
        message: errorMessage,
        errors: error.errors,
      };
    }
  },

  login: async (data: LoginRequest) => {
    set({ isLoading: true, error: undefined });

    try {
      const response = await authService.login(data);

      if (response.success && response.data) {
        const user = response.data.user as User;
        set({
          user,
          isAuthenticated: false, // Don't authenticate until email is verified
          isLoading: false,
          error: undefined,
        });

        return {
          success: true,
          message: response.message || "Login successful",
        };
      } else {
        set({ isLoading: false, error: response.message });
        return {
          success: false,
          message: response.message || "Login failed",
          errors: response.errors,
        };
      }
    } catch (error: any) {
      const errorMessage = error.message || "Network error occurred";
      set({ isLoading: false, error: errorMessage });
      return {
        success: false,
        message: errorMessage,
        errors: error.errors,
      };
    }
  },

  logout: async () => {
    set({ isLoading: true });

    try {
      await authService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: undefined,
      });
    }
  },

  verifyEmail: async (data: VerifyEmailRequest) => {
    set({ isLoading: true, error: undefined });

    try {
      const response = await authService.verifyEmail(data);

      if (response.success) {
        // Update user's email verification status and authenticate
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = {
            ...currentUser,
            isEmailVerified: true,
            // Update onboardingComplete if provided in response
            onboardingComplete:
              response.data?.user?.onboardingComplete ??
              currentUser.onboardingComplete,
          };
          set({
            user: updatedUser,
            isAuthenticated: true, // Now authenticate the user
            isLoading: false,
          });
        } else {
          set({ isLoading: false });
        }

        return {
          success: true,
          message: response.message || "Email verified successfully",
        };
      } else {
        set({ isLoading: false, error: response.message });
        return {
          success: false,
          message: response.message || "Email verification failed",
        };
      }
    } catch (error: any) {
      const errorMessage = error.message || "Network error occurred";
      set({ isLoading: false, error: errorMessage });
      return {
        success: false,
        message: errorMessage,
      };
    }
  },

  resendVerification: async (email: string) => {
    set({ isLoading: true, error: undefined });

    try {
      const response = await authService.resendVerification({ email });

      set({ isLoading: false });

      return {
        success: response.success,
        message:
          response.message ||
          (response.success
            ? "Verification email sent"
            : "Failed to send verification email"),
      };
    } catch (error: any) {
      const errorMessage = error.message || "Network error occurred";
      set({ isLoading: false, error: errorMessage });
      return {
        success: false,
        message: errorMessage,
      };
    }
  },

  checkAuthStatus: async () => {
    set({ isLoading: true });

    try {
      const isAuthenticated = await authService.isAuthenticated();
      console.log("Auth check - isAuthenticated:", isAuthenticated);

      if (isAuthenticated) {
        // Try to fetch fresh user profile to validate token
        try {
          const profileResponse = await authService.getProfile();
          if (profileResponse.success && profileResponse.data) {
            console.log(
              "Auth check - profile fetch successful, user:",
              profileResponse.data.user.email
            );
            set({
              user: profileResponse.data.user as User,
              isAuthenticated: true,
              isLoading: false,
            });
            return;
          }
        } catch (profileError) {
          console.log("Profile fetch failed, using stored data:", profileError);
          // If profile fetch fails, fall back to stored data
          const userData = await authService.getStoredUserData();
          if (userData) {
            console.log(
              "Auth check - using stored data for user:",
              userData.email
            );
            set({
              user: userData as User,
              isAuthenticated: true,
              isLoading: false,
            });
            return;
          }
        }
      }

      // Clear any invalid stored data
      console.log("Auth check - no valid authentication found, clearing data");
      await authService.clearStoredData();
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });
    } catch (error) {
      console.error("Auth status check error:", error);
      // Clear any potentially corrupted data
      await authService.clearStoredData();
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });
    }
  },

  initializeApp: async () => {
    // This is an alias for checkAuthStatus for better semantic meaning
    const { checkAuthStatus } = get();
    await checkAuthStatus();
  },

  updateUser: (userData: Partial<User>) =>
    set((state) => ({
      user: state.user ? { ...state.user, ...userData } : null,
    })),

  setLoading: (isLoading: boolean) => set({ isLoading }),

  setError: (error: string | null) => set({ error: error || undefined }),

  clearError: () => set({ error: undefined }),

  markOnboardingComplete: () => {
    const { user } = get();
    if (user) {
      set({
        user: { ...user, onboardingComplete: true },
      });
    }
  },
}));
